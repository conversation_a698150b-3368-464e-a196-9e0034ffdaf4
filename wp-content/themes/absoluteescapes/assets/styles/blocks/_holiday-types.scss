.holiday-types {
    a {
        font-weight: normal;
        text-decoration: none;

        &:hover,
        &:focus {
            text-decoration: none;

            .holiday-types__image {
                &:after {
                    background: rgba($black, 0.2);
                }
            }

            .holiday-types__heading {
                // Keep white color when in overlay
                .holiday-types__image-overlay & {
                    color: $white;
                }
            }

            .link {
                color: $blue;
            }
        }
    }

    &__inner {
        padding: 60px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 45px 0;
        }
    }

    &__content {
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 0 45px;
            //border-bottom: 1px solid $midlightgrey;
        }
    }

    &__col {
        position: relative;
        &:after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            width: calc(100% - 15px);
            border-bottom: 1px solid $midlightgrey;

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                width: calc(100% - 30px);
            }

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                width: calc(108% + 5px);
                left: calc(-4% - 5px);
            }
        }

        .holiday-types__content-col {
            flex: 0 0 50%;
            max-width: 50%;

            @media only screen and (max-width: 350px) {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        &:first-child,
        &:nth-child(2) {
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                .holiday-types__content-col {
                    flex: 0 0 100%;
                    max-width: 100%;

                    &:nth-child(1) {
                        order: 1;
                    }

                    &:nth-child(2) {
                        order: 2;
                    }

                    .holiday-types__image {
                        text-align: left;
                    }
                }
            }
            @media only screen and (min-width: map-get($grid-breakpoints, md)) {
                .holiday-types__content {
                    padding-top: 0;
                }
            }
        }

        &:last-child,
        &:nth-last-child(2) {
            @media only screen and (min-width: map-get($grid-breakpoints, md)) {
                &:after {
                    display: none;
                }
            }
        }

        &:nth-child(2n + 1) {
            &:after {
                margin-right: -15px;

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    margin-right: 0;
                }
            }

            .holiday-types__content-col {
                &:first-child {
                    padding-right: 50px;

                    @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                        padding-right: 15px;
                    }

                    @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                        padding-right: 5px;
                    }
                }
            }
        }

        &:nth-child(2n + 2) {
            &:after {
                margin-left: -15px;

                @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                    margin-left: 0;
                }
            }

            .holiday-types__content-col {
                &:last-child {
                    padding-left: 50px;

                    @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                        padding-left: 15px;
                    }

                    @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                        padding-left: 5px;
                    }
                }
            }
        }
    }

    &__content-row {
        align-items: center;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            align-items: flex-start;
        }
    }

    &__heading {
        transition: 300ms;

        // When inside image overlay
        .holiday-types__image-overlay & {
            color: $white;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);

            @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
                font-size: 1.5rem;
            }
        }
    }

    &__image-wrapper {
        position: relative;
        overflow: hidden; // Contain the image overlay within wrapper bounds
    }

    &__image {
        position: relative;
        text-align: left;
        display: block;
        overflow: hidden; // Ensure overlay doesn't extend beyond image bounds

        &:after {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba($black, 0);
            transition: 300ms;
            z-index: 3;
            pointer-events: none; // Allow clicks to pass through
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 8px 0;
        }

        img {
            object-fit: cover;
            width: 100%;
            height: auto;
            display: block;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            img {
                display: block;
                width: 100%;
                max-width: 375px;
                margin: 0 auto;
            }
        }

        @media only screen and (max-width: 350px) {
            text-align: left;
        }
    }

    &__image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 2;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        padding: 40px 20px 20px;
        pointer-events: none; // Allow clicks to pass through to the link
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 15px 15px;
        }
    }

    &__icon-wrapper {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    &__icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3e5056;

        img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1);
        }

        svg {
            width: 22px;
            height: 22px;
            fill: white;
            color: white;
        }

        i {
            font-size: 18px;
            color: white;
        }
    }



    &__text {
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 8px 0;
        }
    }

    &__copy {
        p,
        li {
            color: $bluegrey;
        }
    }


}
