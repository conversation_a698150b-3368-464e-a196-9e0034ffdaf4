{"key": "group_all_holidays_featured", "title": "All Holidays Archive - Featured Holidays Panel", "fields": [{"key": "field_675c4000f3000", "label": "Featured Holidays Panel", "name": "featured_holidays_panel", "aria-label": "", "type": "group", "instructions": "Panel that appears between the top CTA and holiday listings on the All Holidays archive page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c4001f3001", "label": "Enable Panel", "name": "enable_panel", "aria-label": "", "type": "true_false", "instructions": "Show the featured holidays panel on the All Holidays archive page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Show featured holidays panel", "default_value": 0, "ui": 1, "ui_on_text": "Yes", "ui_off_text": "No"}, {"key": "field_675c4002f3002", "label": "Panel Title", "name": "panel_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c4001f3001", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Our most popular holidays", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c4003f3003", "label": "Featured Holidays", "name": "featured_holidays", "aria-label": "", "type": "repeater", "instructions": "Select up to 4 holidays to feature", "required": 0, "conditional_logic": [[{"field": "field_675c4001f3001", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "min": 1, "max": 4, "layout": "table", "button_label": "Add Holiday", "collapsed": "", "sub_fields": [{"key": "field_675c4004f3004", "label": "Holiday", "name": "holiday", "aria-label": "", "type": "post_object", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["holiday"], "taxonomy": "", "return_format": "object", "multiple": 0, "allow_null": 0, "ui": 1}]}]}, {"key": "field_675c6000f5001", "label": "Steps Ahead Panel", "name": "steps_ahead_panel", "aria-label": "", "type": "group", "instructions": "Content is managed centrally in Theme Settings. Only background color, curved edge, and on/off controls are available here.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c6001f5002", "label": "Enable Panel", "name": "enable_panel", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "100", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c6004f5005", "label": "Panel Background", "name": "background_color", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "100", "class": "", "id": ""}, "default_value": "#ebf2f1", "enable_opacity": 1, "return_format": "string"}, {"key": "field_675c6005f5006", "label": "Curve Position", "name": "curved_edge", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"none": "None", "top": "Top", "bottom": "Bottom"}, "default_value": "bottom", "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_675c6006f5007", "label": "Curve Orientation", "name": "flip_curved_edge", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}, {"field": "field_675c6005f5006", "operator": "!=", "value": "none"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c6002f5003", "label": "Point Titles", "name": "point_title_color", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "#333333", "enable_opacity": 0, "return_format": "string"}, {"key": "field_675c6003f5004", "label": "Point Background", "name": "column_background_color", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c6001f5002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "enable_opacity": 1, "return_format": "string"}]}], "location": [[{"param": "post_type_archive", "operator": "==", "value": "holiday"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1734622059}