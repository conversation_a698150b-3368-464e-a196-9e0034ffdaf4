<?php

/**
 * Steps Ahead
 * Content comes from Theme Settings, individual blocks control styling only
 */

// Check if Steps Ahead panels are globally enabled
$global_enable = get_field('steps_ahead_global_enable', 'option');
if (!$global_enable) {
    return;
}

// Get global content from Theme Settings
$global_content = get_field('steps_ahead_global_content', 'option');
if (!$global_content) {
    return;
}

// Check if block is enabled (for flexible content blocks)
$enable_block = get_sub_field('enable_block');
if ($enable_block === false) {
    return;
}

// Use global content for title and columns
$heading = $global_content['panel_title'] ?: '';
$columns = $global_content['columns'];

// Use individual block settings for styling
$background_color = get_sub_field('background_color');
$curved_edge = get_sub_field('curved_edge');
$point_title_color = get_sub_field('point_title_color');
$column_background_color = get_sub_field('column_background_color');

// Set up background classes
$background_classes = '';
if ($background_color) {
    $background_classes .= ' has-background';
}
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
}

?>

<section class="steps-ahead">
    <div class="steps-ahead__inner<?php echo $background_classes; ?>" data-aos="fade"<?php if ($background_color) : ?> style="background-color: <?php echo $background_color; ?>;"<?php endif; ?>>
        <div class="container steps-ahead__container">
            <?php if($heading) : ?>
                <div class="steps-ahead__content centre inner-container">
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo esc_html($heading); ?></h2>
                </div>
            <?php endif; ?>

            <?php if($columns && !empty($columns)) : ?>
                <div class="row steps-ahead__row">
                    <?php foreach($columns as $column) : ?>
                        <?php
                        $ctitle = $column['title'] ?: '';
                        $cimage = $column['image'] ?: null;
                        $cpoints = $column['points'] ?: [];
                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre"<?php if($column_background_color) : ?> style="background-color: <?php echo $column_background_color; ?>;"<?php endif; ?>>
                                <?php if($cimage) : ?>
                                    <div class="steps-ahead__col-image">
                                        <img src="<?php echo $cimage['url']; ?>" alt="<?php echo $cimage['alt']; ?>">
                                    </div>
                                <?php endif; ?>

                                <?php if($ctitle) : ?>
                                    <h4 class="steps-ahead__col-heading"><?php echo esc_html($ctitle); ?></h4>
                                <?php endif; ?>

                                <?php if($cpoints && !empty($cpoints)) : ?>
                                    <div class="steps-ahead__col-points">
                                        <?php foreach($cpoints as $point) : ?>
                                            <?php
                                            $point_title = $point['title'] ?: '';
                                            $point_text = $point['text'] ?: '';
                                            ?>
                                            <?php if($point_title || $point_text) : ?>
                                                <div class="steps-ahead__point">
                                                    <?php if($point_title) : ?>
                                                        <h5 class="steps-ahead__point-title"<?php if($point_title_color) : ?> style="color: <?php echo $point_title_color; ?>;"<?php endif; ?>><?php echo esc_html($point_title); ?></h5>
                                                    <?php endif; ?>
                                                    <?php if($point_text) : ?>
                                                        <div class="steps-ahead__point-text">
                                                            <?php echo nl2br(esc_html($point_text)); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->