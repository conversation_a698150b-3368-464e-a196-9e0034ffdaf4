<?php 

/**
 * Favourite Holidays
 */

$heading = get_sub_field('heading');
$copy = get_sub_field('copy');
$holidays = get_sub_field('holidays');

?>

<section class="favourite-holidays">
    <div class="favourite-holidays__inner" data-aos="fade">
        <div class="container favourite-holidays__container">
            <div class="favourite-holidays__content centre inner-container">
                <?php if($heading) : ?>
                    <h2 class="favourite-holidays__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo $heading; ?></h2>
                <?php endif; ?>
                <?php if($copy) : ?>
                    <div class="content-area favourite-holidays__copy">
                        <?php echo $copy; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="container favourite-holidays__container favourite-holidays__container--carousel">
            <?php if($holidays) : ?>
                <div class="favourite-holidays__row">
                    <?php foreach($holidays as $holiday) : ?>
                        <?php

                        $thumbnail = get_the_post_thumbnail_url($holiday);
                        $price = get_field('holiday_price', $holiday->ID);
                        $types = get_the_terms($holiday->ID, 'holiday-type');

                        ?>
                        <div class="favourite-holidays__col">
                            <a href="<?php echo get_the_permalink($holiday->ID); ?>" class="favourite-holidays__link">
                                <div class="favourite-holidays__background" style="background-image: url(<?php echo $thumbnail; ?>);">
                                    <div class="favourite-holidays__col-content text-white">
                                        <h5 class="favourite-holidays__title"><?php echo get_the_title($holiday->ID); ?></h5>
                                        <?php if($price) : ?>
                                            <div class="favourite-holidays__price">
                                                <span><?php _e('From', 'absoluteescapes'); ?> <?php echo $price; ?> <?php the_field('holiday_price_suffix', $holiday->ID); ?></span>
                                            </div>
                                        <?php endif; ?>

                                        <?php if ($types) : ?>
                                            <div class="favourite-holidays__types">
                                                <?php foreach ($types as $type) : ?>
                                                    <?php
                                                    // Skip child terms, only show parent terms
                                                    if ($type->parent) {
                                                        continue;
                                                    }

                                                    $icon = get_field('holiday_type_icon', $type);
                                                    $icon_fa = get_field('holiday_type_icon_fa', $type);
                                                    $colour = get_field('holiday_type_colour', $type);
                                                    ?>

                                                    <div class="favourite-holidays__type">
                                                        <?php if ($icon) : ?>
                                                            <span class="favourite-holidays__icon" style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>">
                                                                <img src="<?php echo $icon['url']; ?>" alt="<?php echo $icon['alt']; ?>">
                                                            </span>
                                                        <?php elseif ($icon_fa) : ?>
                                                            <span class="favourite-holidays__icon" style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>">
                                                                <i class="<?php echo $icon_fa; ?>"></i>
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>

                <span class="favourite-holidays__button favourite-holidays__button--prev flickity-icon flickity-icon--prev"><i class="fal fa-chevron-double-left"></i></span>
                <span class="favourite-holidays__button favourite-holidays__button--next flickity-icon flickity-icon--next"><i class="fal fa-chevron-double-right"></i></span>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .favourite-holidays -->